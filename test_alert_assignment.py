#!/usr/bin/env python3
"""
Script de test pour l'attribution d'alertes
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_alert_endpoints():
    """Test des endpoints d'alertes"""
    
    print("🔍 Test des endpoints d'alertes...")
    
    # 1. Test liste des alertes
    print("\n1. Test liste des alertes:")
    try:
        response = requests.get(f"{BASE_URL}/alerts/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Nombre d'alertes: {data.get('count', 'N/A')}")
            if 'results' in data and len(data['results']) > 0:
                first_alert = data['results'][0]
                print(f"   Première alerte ID: {first_alert.get('id')}")
                print(f"   Statut: {first_alert.get('alert_status')}")
                print(f"   Assignée à: {first_alert.get('assigned_to_name', 'Non assignée')}")
        elif response.status_code == 401:
            print("   ❌ Authentification requise")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # 2. Test alertes actives
    print("\n2. Test alertes actives:")
    try:
        response = requests.get(f"{BASE_URL}/alerts/active/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Alertes actives: {data.get('count', 'N/A')}")
        elif response.status_code == 401:
            print("   ❌ Authentification requise")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # 3. Test agents disponibles
    print("\n3. Test agents disponibles:")
    try:
        response = requests.get(f"{BASE_URL}/investigations/available-agents/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Nombre d'agents: {data.get('count', 'N/A')}")
            if 'agents' in data and len(data['agents']) > 0:
                first_agent = data['agents'][0]
                print(f"   Premier agent: {first_agent.get('full_name')}")
                print(f"   Charge de travail: {first_agent.get('total_workload')}")
        elif response.status_code == 401:
            print("   ❌ Authentification requise")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # 4. Test statistiques dashboard
    print("\n4. Test statistiques dashboard:")
    try:
        response = requests.get(f"{BASE_URL}/stats/dashboard/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Total détections: {data.get('total_detections', 'N/A')}")
            print(f"   Alertes actives: {data.get('active_alerts', 'N/A')}")
            print(f"   Investigations en attente: {data.get('pending_investigations', 'N/A')}")
        elif response.status_code == 401:
            print("   ❌ Authentification requise")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_alert_assignment_endpoint():
    """Test spécifique de l'endpoint d'assignation"""
    
    print("\n🎯 Test endpoint d'assignation d'alertes...")
    
    # Test avec une alerte fictive (ID 1)
    alert_id = 1
    test_data = {
        "assigned_to": 1,  # ID agent fictif
        "notes": "Test d'assignation automatique"
    }
    
    try:
        response = requests.patch(
            f"{BASE_URL}/alerts/{alert_id}/assign/",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Assignation réussie!")
            print(f"   Message: {data.get('message')}")
            print(f"   Agent: {data.get('agent_info', {}).get('name')}")
        elif response.status_code == 401:
            print("   ❌ Authentification requise")
        elif response.status_code == 404:
            print("   ❌ Alerte ou agent introuvable")
        elif response.status_code == 400:
            print(f"   ❌ Erreur de validation: {response.json()}")
        else:
            print(f"   ❌ Erreur: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 Test des fonctionnalités d'alertes Gold Sentinel")
    print("=" * 50)
    
    test_alert_endpoints()
    test_alert_assignment_endpoint()
    
    print("\n" + "=" * 50)
    print("✅ Tests terminés!")
    print("\nNote: Les erreurs 401 (Authentification requise) sont normales")
    print("car les endpoints nécessitent une authentification JWT.")
