import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { imagesAPI } from '../services/api';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  MenuItem,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Chip,
  ImageList,
  ImageListItem,
  ImageListItemBar
} from '@mui/material';
import {
  Search,
  FilterList,
  LocationOn,
  CalendarToday,
  Satellite,
  ZoomIn,
  Download,
  Info
} from '@mui/icons-material';
import { imageService } from '../services';

interface Image {
  id: number;
  name: string;
  capture_date: string;
  satellite_source: string;
  cloud_coverage: number;
  spatial_resolution?: number;
  gee_asset_id: string;
  processing_status: string;
  region: number;
  region_name?: string;
  center_lat: number;
  center_lon: number;
  created_at: string;
  // Propriétés optionnelles pour compatibilité
  filename?: string;
  url?: string;
  status?: string;
  satellite?: string;
  resolution?: string;
  metadata?: Record<string, string>;
  thumbnail_url?: string;
  download_url?: string;
  full_url?: string;
}

const ImageCard = ({ image, onViewDetails }: { image: Image; onViewDetails: (image: Image) => void }) => (
  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
    <Box
      sx={{
        position: 'relative',
        paddingTop: '75%', // 4:3 aspect ratio
        overflow: 'hidden',
        bgcolor: 'grey.100'
      }}
    >
      {/* Image placeholder avec informations */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'primary.light',
          color: 'white'
        }}
      >
        <Satellite sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="body2" align="center">
          {image.name}
        </Typography>
        <Chip
          label={image.processing_status}
          size="small"
          color={image.processing_status === 'COMPLETED' ? 'success' : 'warning'}
          sx={{ mt: 1 }}
        />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          display: 'flex',
          gap: 1
        }}
      >
        <Tooltip title="Voir les détails">
          <IconButton
            size="small"
            onClick={() => onViewDetails(image)}
            sx={{ bgcolor: 'rgba(255, 255, 255, 0.8)' }}
          >
            <Info />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
    <CardContent>
      <Typography variant="h6" gutterBottom noWrap>
        {image.name}
      </Typography>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <LocationOn fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary">
          {image.region_name || `Région ${image.region}`}
        </Typography>
      </Box>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <CalendarToday fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary">
          {new Date(image.capture_date).toLocaleDateString()}
        </Typography>
      </Box>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <Satellite fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary">
          {image.satellite_source}
        </Typography>
      </Box>
      <Box display="flex" alignItems="center" gap={1}>
        <Typography variant="body2" color="text.secondary">
          Couverture nuageuse: {image.cloud_coverage}%
        </Typography>
      </Box>
    </CardContent>
  </Card>
);

export const ImagesPage = () => {
  const { user, hasAuthority } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [regionFilter, setRegionFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState<Image | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // Récupération des vraies données avec pagination
  const { data: imagesResponse, isLoading, error } = useQuery({
    queryKey: ['images', currentPage, regionFilter],
    queryFn: () => imagesAPI.getAll({
      page: currentPage,
      page_size: pageSize,
      ...(regionFilter !== 'all' && { region: regionFilter })
    }),
    onSuccess: (data) => {
      console.log('✅ API Success - Raw data:', data);
      console.log('✅ Data type:', typeof data);
      console.log('✅ Is array:', Array.isArray(data));
      console.log('✅ Pagination info:', {
        count: data?.count,
        next: data?.next,
        previous: data?.previous,
        results_length: data?.results?.length
      });
    },
    onError: (error) => {
      console.error('❌ API Error:', error);
      console.error('❌ Error details:', error.response?.data);
    }
  });

  // Debug des données
  console.log('🔍 Images state:', { imagesResponse, isLoading, error });
  console.log('🔍 Auth token:', localStorage.getItem('access_token') ? 'Present' : 'Missing');

  // Gestion sécurisée des données d'images avec pagination
  const images = imagesResponse?.results || [];
  const totalCount = imagesResponse?.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  console.log('🔍 Processed data:', {
    images,
    count: images.length,
    totalCount,
    totalPages,
    currentPage
  });

  // Filtrage côté client pour la recherche textuelle
  const filteredImages = images.filter(image => {
    if (!image) return false;
    const regionName = image.region_name || image.region || '';
    const imageName = image.name || '';
    const searchText = searchTerm.toLowerCase();
    const matchesSearch = regionName.toLowerCase().includes(searchText) ||
                         imageName.toLowerCase().includes(searchText);
    return matchesSearch;
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Chargement des images...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Erreur lors du chargement des images. Vérifiez votre connexion et réessayez.
        </Alert>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 3 }}>
          Images Satellites
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Aucune image disponible pour le moment.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 3 }}>
        Images Satellites
      </Typography>

      <Box display="flex" gap={2} mb={3}>
        <TextField
          fullWidth
          placeholder="Rechercher par région..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <Search sx={{ mr: 1, color: 'action.active' }} />
          }}
        />
        <TextField
          select
          label="Région"
          value={regionFilter}
          onChange={(e) => {
            setRegionFilter(e.target.value);
            setCurrentPage(1); // Réinitialiser à la première page
          }}
          sx={{ minWidth: 200 }}
        >
          <MenuItem value="all">Toutes les régions</MenuItem>
          <MenuItem value="BONDOUKOU">BONDOUKOU</MenuItem>
          <MenuItem value="BOUNA">BOUNA</MenuItem>
          <MenuItem value="TANDA">TANDA</MenuItem>
          <MenuItem value="NASSIAN">NASSIAN</MenuItem>
        </TextField>
      </Box>

      {/* Informations de pagination */}
      {totalCount > 0 && (
        <Box display="flex" justifyContent="between" alignItems="center" mb={2}>
          <Typography variant="body2" color="text.secondary">
            {filteredImages.length} image{filteredImages.length > 1 ? 's' : ''} affichée{filteredImages.length > 1 ? 's' : ''}
            {searchTerm && ` (recherche: "${searchTerm}")`}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total: {totalCount} image{totalCount > 1 ? 's' : ''} • Page {currentPage} sur {totalPages}
          </Typography>
        </Box>
      )}

      {filteredImages.length === 0 ? (
        <Alert severity="info" sx={{ mt: 3 }}>
          {totalCount === 0
            ? 'Aucune image disponible dans la base de données.'
            : searchTerm
              ? `Aucune image trouvée pour "${searchTerm}". Essayez de modifier votre recherche.`
              : 'Aucune image trouvée pour cette région.'
          }
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {filteredImages.map((image) => (
              <Grid item xs={12} sm={6} md={4} key={image.id}>
                <ImageCard
                  image={image}
                  onViewDetails={setSelectedImage}
                />
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center" alignItems="center" mt={4} gap={2}>
              <Button
                variant="outlined"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              >
                Précédent
              </Button>

              <Typography variant="body2" sx={{ mx: 2 }}>
                Page {currentPage} sur {totalPages}
              </Typography>

              <Button
                variant="outlined"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              >
                Suivant
              </Button>
            </Box>
          )}
        </>
      )}

      <Dialog
        open={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        maxWidth="lg"
        fullWidth
      >
        {selectedImage && (
          <>
            <DialogTitle>
              Détails de l'image
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid sx={{ xs: 12 }}>
                  <Box
                    component="img"
                    src={selectedImage.full_url}
                    alt={selectedImage.region}
                    sx={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: 1
                    }}
                  />
                </Grid>
                <Grid sx={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Région
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedImage.region}
                  </Typography>
                </Grid>
                <Grid sx={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date de capture
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {new Date(selectedImage.capture_date).toLocaleString()}
                  </Typography>
                </Grid>
                <Grid sx={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Satellite
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedImage.satellite}
                  </Typography>
                </Grid>
                <Grid sx={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Résolution
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedImage.resolution} m/pixel
                  </Typography>
                </Grid>
                {selectedImage.metadata && (
                  <Grid sx={{ xs: 12 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Métadonnées
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      {Object.entries(selectedImage.metadata).map(([key, value]) => (
                        <Chip
                          key={key}
                          label={`${key}: ${value}`}
                          size="small"
                          sx={{ mr: 1, mb: 1 }}
                        />
                      ))}
                    </Box>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                href={selectedImage.download_url}
                download
                startIcon={<Download />}
              >
                Télécharger
              </Button>
              <Button onClick={() => setSelectedImage(null)}>
                Fermer
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};