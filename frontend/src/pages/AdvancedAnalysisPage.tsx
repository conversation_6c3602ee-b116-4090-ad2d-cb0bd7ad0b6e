import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  PlayIcon,
  DocumentTextIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import analysisService from '../services/analysis.service';
import financialRiskService from '../services/financial-risk.service';
import type { 
  AnalysisStats, 
  AutomatedAnalysisRun,
  AnalysisTemplate,
  AnalysisSchedule 
} from '../services/analysis.service';
import type { RiskDashboardData } from '../services/financial-risk.service';
import { Card, Button, Loading } from '../components/ui';
import { AutomatedAnalysisLauncher } from '../components/analysis/AutomatedAnalysisLauncher';
import { FinancialRiskDashboard } from '../components/analysis/FinancialRiskDashboard';
import { AnalysisTemplateManager } from '../components/analysis/AnalysisTemplateManager';
import { AnalysisScheduleManager } from '../components/analysis/AnalysisScheduleManager';
import { BulkAnalysisRunner } from '../components/analysis/BulkAnalysisRunner';
import toast from 'react-hot-toast';

export const AdvancedAnalysisPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  // États locaux
  const [activeTab, setActiveTab] = useState<'launcher' | 'financial' | 'templates' | 'schedules' | 'bulk'>('launcher');
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  // Récupération des données
  const { 
    data: analysisStats, 
    isLoading: statsLoading 
  } = useQuery({
    queryKey: ['analysis-stats', selectedPeriod],
    queryFn: () => analysisService.getAnalysisStats(selectedPeriod),
    refetchInterval: 30000, // Refresh toutes les 30 secondes
  });

  const { 
    data: riskDashboard, 
    isLoading: riskLoading 
  } = useQuery({
    queryKey: ['risk-dashboard', selectedPeriod],
    queryFn: () => financialRiskService.getRiskDashboardData(selectedPeriod),
    refetchInterval: 60000, // Refresh toutes les minutes
  });

  const { data: templates } = useQuery({
    queryKey: ['analysis-templates'],
    queryFn: () => analysisService.getAnalysisTemplates(),
  });

  const { data: schedules } = useQuery({
    queryKey: ['analysis-schedules'],
    queryFn: () => analysisService.getAnalysisSchedules(),
  });

  // Mutation pour lancement d'analyse automatisée
  const runAnalysisMutation = useMutation({
    mutationFn: (config: AutomatedAnalysisRun) => analysisService.runAutomatedAnalysis(config),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-stats'] });
      queryClient.invalidateQueries({ queryKey: ['analysis-jobs'] });
      toast.success(`${data.jobs_created.length} analyse(s) lancée(s) avec succès`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du lancement de l\'analyse');
    }
  });

  // Vérification des permissions
  if (!permissions.canLaunchAnalysis()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 text-center">
          <ExclamationTriangleIcon className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous n'avez pas les permissions nécessaires pour accéder aux analyses avancées.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-6 text-white shadow-xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Analyses Avancées & Risques Financiers
            </h1>
            <p className="text-purple-100">
              Lancement d'analyses automatisées et évaluation des risques financiers
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
              <BeakerIcon className="w-8 h-8 text-purple-900" />
            </div>
          </div>
        </div>
        
        {/* Statistiques rapides */}
        {analysisStats && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-purple-200">Analyses totales</div>
              <div className="text-xl font-bold">{analysisStats.total_analyses}</div>
            </div>
            <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-purple-200">En cours</div>
              <div className="text-xl font-bold">{analysisStats.running_analyses}</div>
            </div>
            <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-purple-200">Surface analysée</div>
              <div className="text-xl font-bold">
                {(analysisStats.total_area_analyzed_hectares / 1000).toFixed(1)}k ha
              </div>
            </div>
            <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-purple-200">Détections</div>
              <div className="text-xl font-bold">{analysisStats.total_detections}</div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Sélecteur de période */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Période d'analyse</h3>
            <div className="flex space-x-2">
              {(['week', 'month', 'quarter', 'year'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    selectedPeriod === period
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  {period === 'week' ? 'Semaine' :
                   period === 'month' ? 'Mois' :
                   period === 'quarter' ? 'Trimestre' : 'Année'}
                </button>
              ))}
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Navigation par onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="p-1">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('launcher')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'launcher'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <PlayIcon className="w-4 h-4 mr-2" />
              Lancement Analyses
            </button>
            <button
              onClick={() => setActiveTab('financial')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'financial'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <CurrencyDollarIcon className="w-4 h-4 mr-2" />
              Risques Financiers
            </button>
            <button
              onClick={() => setActiveTab('templates')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'templates'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <DocumentTextIcon className="w-4 h-4 mr-2" />
              Templates ({templates?.length || 0})
            </button>
            <button
              onClick={() => setActiveTab('schedules')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'schedules'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <CalendarIcon className="w-4 h-4 mr-2" />
              Programmations ({schedules?.length || 0})
            </button>
            <button
              onClick={() => setActiveTab('bulk')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'bulk'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <ChartBarIcon className="w-4 h-4 mr-2" />
              Analyses en Lot
            </button>
          </div>
        </Card>
      </motion.div>

      {/* Contenu des onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {activeTab === 'launcher' && (
          <AutomatedAnalysisLauncher
            onLaunch={(config) => runAnalysisMutation.mutate(config)}
            isLoading={runAnalysisMutation.isPending}
          />
        )}

        {activeTab === 'financial' && (
          <FinancialRiskDashboard
            dashboardData={riskDashboard}
            isLoading={riskLoading}
            period={selectedPeriod}
          />
        )}

        {activeTab === 'templates' && (
          <AnalysisTemplateManager
            templates={templates || []}
            onTemplateChange={() => {
              queryClient.invalidateQueries({ queryKey: ['analysis-templates'] });
            }}
          />
        )}

        {activeTab === 'schedules' && (
          <AnalysisScheduleManager
            schedules={schedules || []}
            templates={templates || []}
            onScheduleChange={() => {
              queryClient.invalidateQueries({ queryKey: ['analysis-schedules'] });
            }}
          />
        )}

        {activeTab === 'bulk' && (
          <BulkAnalysisRunner
            analysisStats={analysisStats}
            onBulkRun={() => {
              queryClient.invalidateQueries({ queryKey: ['analysis-stats'] });
              queryClient.invalidateQueries({ queryKey: ['analysis-jobs'] });
            }}
          />
        )}
      </motion.div>

      {/* Résumé des tendances */}
      {analysisStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Tendances d'analyse - {selectedPeriod === 'week' ? 'Cette semaine' :
                                    selectedPeriod === 'month' ? 'Ce mois' :
                                    selectedPeriod === 'quarter' ? 'Ce trimestre' : 'Cette année'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Analyses par type */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Analyses par type</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Détection minière</span>
                    <span className="font-medium">{analysisStats.analyses_by_type.MINING_DETECTION}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Changements</span>
                    <span className="font-medium">{analysisStats.analyses_by_type.CHANGE_DETECTION}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Végétation</span>
                    <span className="font-medium">{analysisStats.analyses_by_type.VEGETATION_ANALYSIS}</span>
                  </div>
                </div>
              </div>

              {/* Régions à risque */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Régions à risque</h4>
                <div className="space-y-2">
                  {analysisStats.top_regions.slice(0, 3).map((region, index) => (
                    <div key={region.region} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{region.region}</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{region.count}</span>
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                          region.risk_level === 'HIGH' ? 'bg-red-100 text-red-800' :
                          region.risk_level === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {region.risk_level}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Métriques de performance */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Performance</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Temps de traitement</span>
                    <span className="font-medium">
                      {(analysisStats.total_processing_time_hours / analysisStats.completed_analyses || 0).toFixed(1)}h moy.
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Taux de réussite</span>
                    <span className="font-medium">
                      {((analysisStats.completed_analyses / analysisStats.total_analyses) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Alertes générées</span>
                    <span className="font-medium">{analysisStats.total_alerts_generated}</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
